const { scrapeWorldAthletics } = require('./Scripts/scraper-world-athletics'); //importa script 1
const { scrapeOlympics } = require('./Scripts/scraper-olympics'); // importa script 2
const { 
  scrapeAthleteDetails, //importa função para rodar até o fim
  scrapeAthleteDetailsLimited, //importa função para rodar apenas algumas páginas
  resumeAthleteDetails, //importa função para retomar do checkpoint
  resumeAthleteDetailsWithLimit, //importa função para retomar com limite de páginas
  clearCheckpoint //importa função para limpar checkpoint
} = require('./Scripts/scraper-athlete-details'); // importa script 1 (parte de detalhes - resultados)

const {
  runEtapa1, //importa função para migração completa
  runEtapa2, //importa função para verificar dados olímpicos
  runEtapa3  //importa função para calcular scores
} = require('./Scripts/script3-unified-migration'); // importa scraper de migração unificada

const db = require('./models'); // importa models

async function main() {
  const scraper = process.argv[2]; // Ex: 'world-athletics', 'olympics', 'athlete-details'
  const param = process.argv[3]; // Ex: 'men', 'women', etc
  const option = process.argv[4]; // Ex: 'limited', 'resume', 'clear', número de páginas
  const extraParam = process.argv[5]; // Ex: número após 'resume'

  if (!scraper) {
    console.log('Uso: node core.js <scraper> [parametros] [opcoes]');
    console.log('Exemplos:');
    console.log('  node core.js world-athletics men');
    console.log('  node core.js olympics');
    console.log('  node core.js athlete-details men');
    console.log('  node core.js athlete-details men limited    # Apenas 10 páginas');
    console.log('  node core.js athlete-details men 5          # Apenas 5 páginas');
    console.log('  node core.js athlete-details men resume     # Retoma do checkpoint');
    console.log('  node core.js athlete-details men resume 10  # Retoma + 10 páginas');
    console.log('  node core.js athlete-details men clear      # Limpa progresso e recomeça');
    process.exit(1);
  }

  await db.sequelize.authenticate(); // Conecta com banco
  console.log('Conectado ao banco via Sequelize!');

  if (scraper === 'world-athletics') { 
    const gender = param || 'men'; // se não passar parâmetro, assume masculino
    console.log(`Rodando scraper World Athletics para gênero: ${gender}`); 
    const atletas = await scrapeWorldAthletics(gender); // executa o script 1
    console.log(`Total de atletas coletados: ${atletas.length}`); // mostra quantos atletas foram coletados
    for (const atleta of atletas) { // para cada atleta, salva no banco
      console.log("Salvando atleta: ", atleta.athlete_name); // mostra o nome do atleta que está salvando
      await db.WorldAthletics.upsert(atleta); // salva no banco
    }
    console.log('Dados salvos na tabela world_athletics!');
  } else if (scraper === 'olympics') {
    console.log('Rodando scraper Olympics...');
    const result = await scrapeOlympics(); // executa o script 2

    console.log(`Atletas individuais coletados: ${result.individual.length}`);
    console.log(`Equipes coletadas: ${result.equipe.length}`);

    // Salva atletas individuais
    for (const atleta of result.individual) {
      console.log("Salvando atleta individual: ", atleta.athlete_name);
      await db.OlympicsIndividual.upsert({
        athlete_name: atleta.athlete_name,
        event: atleta.event,
        country: atleta.country,
        result: atleta.result,
        position: atleta.position
      });
    }

    // Salva equipes
    for (const equipe of result.equipe) {
      console.log("Salvando equipe: ", equipe.event);
      await db.OlympicsEquipe.upsert({
        event: equipe.event,
        equipe: equipe.equipe,
        result: equipe.result,
        position: equipe.position
      });
    }

    console.log('Dados salvos nas tabelas olympics_individual e olympics_equipe!');
  } else if (scraper === 'athlete-details') { // executa o script 1 (parte de detalhes - resultados)
    const gender = param || 'men'; // se não passar parâmetro, assume masculino

    console.log(`Rodando scraper Athlete Details para gênero: ${gender}`);

    // Verifica as opções especiais
    if (option === 'clear') { // se passar a opção 'clear', limpa o checkpoint
      console.log('Limpando checkpoint anterior...');
      clearCheckpoint(gender);
      console.log('Checkpoint limpo! Execute novamente sem a opção "clear".');
      return;
    }

    let result; // variável para armazenar o resultado da execução

    if (option === 'resume') { // se passar a opção 'resume', retoma do checkpoint
      if (extraParam && !isNaN(parseInt(extraParam))) { // se passar um número após 'resume', retoma + X páginas
        const additionalPages = parseInt(extraParam); // converte o parâmetro para número
        console.log(`Retomando do checkpoint + ${additionalPages} páginas...`);
        result = await resumeAthleteDetailsWithLimit(gender, additionalPages, db.TestResults);
      } else {
        console.log('Retomando do checkpoint...');
        result = await resumeAthleteDetails(gender, db.TestResults);
      }
    } else if (option === 'limited') { // se passar a opção 'limited', processa apenas 10 páginas
      console.log('Modo limitado: processando apenas 10 páginas...');
      result = await scrapeAthleteDetailsLimited(gender, 10, db.TestResults);
    } else if (!isNaN(parseInt(option))) { // se passar um número, processa apenas aquela quantidade de páginas
      const maxPages = parseInt(option);
      console.log(`Processando apenas ${maxPages} páginas...`);
      result = await scrapeAthleteDetailsLimited(gender, maxPages, db.TestResults);
    } else { // se não passar nenhuma opção, processa todas as páginas
      console.log('Processando todas as páginas...');
      result = await scrapeAthleteDetails(gender, { dbModel: db.TestResults });
    }

    if (result.success) { // se a execução foi bem sucedida, mostra quantos atletas foram processados
      console.log(`Processamento concluído! ${result.totalProcessed} atletas processados.`);
      console.log('Dados salvos na tabela test_results!');
    } else { 
      console.error(`Erro durante o processamento: ${result.error}`);
      console.log('Progresso salvo em checkpoint - use "resume" para continuar.');
    }
  } else if (scraper === 'unified-migration') {
    const etapa = param || 'help';

    console.log(`Rodando scraper Unified Migration - ${etapa}`);

    if (etapa === 'etapa1') {
      await runEtapa1();
    } else if (etapa === 'etapa2') {
      await runEtapa2();
    } else if (etapa === 'etapa3') {
      await runEtapa3();
    } else {
      console.log('Etapas disponíveis: etapa1, etapa2, etapa3');
      console.log('Exemplos:');
      console.log('  node core.js unified-migration etapa1  # Migração completa');
      console.log('  node core.js unified-migration etapa2  # Status olímpico');
      console.log('  node core.js unified-migration etapa3  # Cálculo de scores');
    }
  } else {
    console.log('Scraper não reconhecido:', scraper); // se não passar um scraper válido, da erro
  }

  await db.sequelize.close(); // fecha conexão com banco
}

main().catch(err => { // se der erro na execução, mostra o erro e fecha o processo
  console.error('Erro no core:', err);
  process.exit(1);
}); 