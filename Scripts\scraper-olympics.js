
const https = require('https');
const { URL } = require('url');

// Função para decodificar entidades HTML como &#197; para caracteres normais
function decodeHtmlEntities(text) {
  if (!text) return text;

  return text
    // Converte entidades numéricas decimais (ex: &#197; -> Å)
    .replace(/&#(\d+);/g, (match, dec) => String.fromCharCode(dec))
    // Converte entidades numéricas hexadecimais (ex: &#xC5; -> Å)
    .replace(/&#x([0-9a-f]+);/g, (match, hex) => String.fromCharCode(parseInt(hex, 16)))
    // Converte entidades HTML nomeadas comuns
    .replace(/&quot;/g, '"')
    .replace(/&apos;/g, "'")
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&');
}

// Array dos eventos
const eventos = [
    { nome: '10.000m Masculino', slug: 'men-10-000m' },
    { nome: '10.000m Feminino', slug: 'women-10-000m' },
    { nome: '100m Feminino', slug: 'women-100m' },
    { nome: '100m Masculino', slug: 'men-100m' },
    { nome: '1500m Feminino', slug: 'women-1500m' },
    { nome: '1500m Masculino', slug: 'men-1500m' },
    { nome: '200m Feminino', slug: 'women-200m' },
    { nome: '200m Masculino', slug: 'men-200m' },
    { nome: '400m Feminino', slug: 'women-400m' },
    { nome: '400m Masculino', slug: 'men-400m' },
    { nome: '5000m Feminino', slug: 'women-5000m' },
    { nome: '5000m Masculino', slug: 'men-5000m' },
    { nome: '800m Feminino', slug: 'women-800m' },
    { nome: '800m Masculino', slug: 'men-800m' },
    { nome: '100m com Barreira Feminino', slug: 'women-100m-hurdles' },
    { nome: '110m com Barreira Masculino', slug: 'men-110m-hurdles' },
    { nome: '400m com Barreira Feminino', slug: 'women-400m-hurdles' },
    { nome: '400m com Barreira Masculino', slug: 'men-400m-hurdles' },
    { nome: '20km Marcha Atlética Feminina', slug: 'women-20km-race-walk' },
    { nome: '20km Marcha Atlética Masculina', slug: 'men-20km-race-walk' },
    { nome: '3000m com Obstáculos Feminino', slug: 'women-3000m-steeplechase' },
    { nome: '3000m com Obstáculos Masculino', slug: 'men-3000m-steeplechase' },
    { nome: 'Salto em Altura Feminino', slug: 'women-high-jump' },
    { nome: 'Salto em Altura Masculino', slug: 'men-high-jump' },
    { nome: 'Salto em Distância Feminino', slug: 'women-long-jump' },
    { nome: 'Salto em Distância Masculino', slug: 'men-long-jump' },
    { nome: 'Salto Triplo Feminino', slug: 'women-triple-jump' },
    { nome: 'Salto Triplo Masculino', slug: 'men-triple-jump' },
    { nome: 'Salto com Vara Feminino', slug: 'women-pole-vault' },
    { nome: 'Salto com Vara Masculino', slug: 'men-pole-vault' },
    { nome: 'Arremesso de Peso Feminino', slug: 'women-shot-put' },
    { nome: 'Arremesso de Peso Masculino', slug: 'men-shot-put' },
    { nome: 'Lançamento de Disco Feminino', slug: 'women-discus-throw' },
    { nome: 'Lançamento de Disco Masculino', slug: 'men-discus-throw' },
    { nome: 'Lançamento de Dardo Feminino', slug: 'women-javelin-throw' },
    { nome: 'Lançamento de Dardo Masculino', slug: 'men-javelin-throw' },
    { nome: 'Lançamento de Martelo Feminino', slug: 'women-hammer-throw' },
    { nome: 'Lançamento de Martelo Masculino', slug: 'men-hammer-throw' },
    { nome: 'Decatlo Masculino', slug: 'men-decathlon' },
    { nome: 'Heptatlo Feminino', slug: 'women-heptathlon' },
    { nome: 'Maratona Feminina', slug: 'women-marathon' },
    { nome: 'Maratona Masculina', slug: 'men-marathon' },
    // Eventos de equipe (revezamentos)
    { nome: 'Revezamento 4x100m Masculino', slug: 'men-4-x-100m-relay' }, //42
    { nome: 'Revezamento 4x100m Feminino', slug: 'women-4-x-100m-relay' },
    { nome: 'Revezamento 4x400m Masculino', slug: 'men-4-x-400m-relay' },
    { nome: 'Revezamento 4x400m Feminino', slug: 'women-4-x-400m-relay' },
    { nome: 'Revezamento 4x400m Misto', slug: '4-x-400m-relay-mixed' },
    { nome: 'Maratona de Marcha Atlética Revezamento Misto', slug: 'marathon-race-walk-relay-mixed'}

];


// Função para criar delay entre eventos (3 segundos)
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

function fetchHTML(url, callback) {
    const reqUrl = new URL(url);

    const options = {
        hostname: reqUrl.hostname,
        path: reqUrl.pathname + reqUrl.search,
        headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
            'Accept-Encoding': 'identity',
            'Connection': 'close'
        },
        port: 443,
        method: 'GET',
        timeout: 10000,
    };

    const req = https.request(options, (res) => {
        // Define o encoding UTF-8 para garantir que caracteres especiais sejam lidos corretamente
        res.setEncoding('utf8');
        let html = '';
        res.on('data', chunk => html += chunk);
        res.on('end', () => callback(null, html));
    });

    req.on('timeout', () => {
        req.destroy();
        callback(new Error('Timeout da requisição'), null);
    });

    req.on('error', (err) => {
        callback(err, null);
    });

    req.end();
}

function substringFrom(start, end, text) {
    const startIndex = text.indexOf(start);
    if (startIndex === -1) return null;
    const endIndex = text.indexOf(end, startIndex + start.length);
    if (endIndex === -1) return null;
    return text.substring(startIndex + start.length, endIndex).trim();
}

function extractCountry(element) {
    // Procura por padrões de país como: data-cy="ETH", data-cy="USA", etc.
    const countryMatch = element.match(/data-cy="([A-Z]{3})"/);
    if (countryMatch) {
        return countryMatch[1];
    }
    // Padrão alternativo: <span data-cy="ETH" class="...">ETH</span>
    const countryPattern = substringFrom('<span data-cy="', '"', element);
    if (countryPattern && countryPattern.length === 3 && /^[A-Z]+$/.test(countryPattern)) {
        return countryPattern;
    }
    return null;
}

// Função para processar um evento e retornar dados dos atletas
function processEventAndReturn(evento) {
    return new Promise((resolve, reject) => {
        const eventUrl = `https://www.olympics.com/pt/olympic-games/paris-2024/results/athletics/${evento.slug}`;
        fetchHTML(eventUrl, (err, html) => {
            if (err) {
                reject(err);
                return;
            }
            const { individual, equipe } = extractAthletesFromHtml(html, evento.nome);
            resolve({ individual, equipe });
        });
    });
}

function extractAthletesFromHtml(html, eventoNome) {
    const individual = [];
    const equipe = [];
    const hasAthleteNames = html.includes('data-cy="athlete-name"');
    if (hasAthleteNames) {
        // Individual
    const athleteBlocks = html.split('data-cy="athlete-name"');
    athleteBlocks.forEach((element, index) => {
        if (index === 0) return;
        const athleteName = substringFrom('class="sc-d8cd2c5-3 ewwlft">', '</h3>', element);
        const previousElement = athleteBlocks[index - 1] || '';
        const countryCode = extractCountry(previousElement + element);
        const athleteResult = substringFrom('<span data-cy="result-info-content">', '</span>', element);
        const athletePosition = index;
        if (athleteName) {
                individual.push({
                    athlete_name: decodeHtmlEntities(athleteName),
                    event: eventoNome,
                    country: countryCode || null,
                    result: athleteResult || 'N/A',
                    position: athletePosition
                });
            }
        });
    } else {
        // Equipe
    const resultBlocks = html.split('data-cy="team-result-row"');
    resultBlocks.forEach((element, index) => {
            if (index === 0) return;
        const countryName = substringFrom('<span class="sc-4ffa8ad5-8 iAyztF">', '</span>', element);
        const athleteResult = substringFrom('<span data-cy="result-info-content">', '</span>', element);
        const athletePosition = index;
            if (countryName) {
                equipe.push({
                    event: eventoNome,
                    equipe: countryName,
                    result: athleteResult || 'N/A',
                    position: athletePosition
                });
        }
    });
}
    return { individual, equipe };
}

async function scrapeOlympics() {
    const allIndividual = [];
    const allEquipe = [];
    for (const evento of eventos) {
        try {
            const { individual, equipe } = await processEventAndReturn(evento);
            allIndividual.push(...individual);
            allEquipe.push(...equipe);
            // await delay(3000);
        } catch (err) {
            // Continua mesmo se um evento falhar
        }
    }
    return { individual: allIndividual, equipe: allEquipe };
}

module.exports = { scrapeOlympics };






// // Rodar todos os eventos:
// scrapeOlympics().then(atletas => {
//   console.log(atletas);
//   console.log('Todos os eventos processados!');
// }).catch(err => console.error('Erro:', err.message));
//
// // Rodar um evento específico:
// processEventAndReturn(eventos[42]).then(atletas => {
//   console.log(atletas);
//   console.log('Teste concluído!');
// }).catch(err => console.error('Erro:', err.message));

