const https = require('https');
const { URL } = require('url');

// Função para decodificar entidades HTML como &#197; para caracteres normais
function decodeHtmlEntities(text) {
  if (!text) return text;

  return text
    // Converte entidades numéricas decimais (ex: &#197; -> Å)
    .replace(/&#(\d+);/g, (match, dec) => String.fromCharCode(dec))
    // Converte entidades numéricas hexadecimais (ex: &#xC5; -> Å)
    .replace(/&#x([0-9a-f]+);/g, (match, hex) => String.fromCharCode(parseInt(hex, 16)))
    // Converte entidades HTML nomeadas comuns
    .replace(/&quot;/g, '"')
    .replace(/&apos;/g, "'")
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&'); 
}
// URL base para a paginação 
const baseUrlTemplate = 'https://worldathletics.org/world-rankings/overall-ranking';

//função para criar dalay de 2seg entre as requisições
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function fetchHTML(url, callback) {
  const reqUrl = new URL(url);

  const options = {
    hostname: reqUrl.hostname,
    path: reqUrl.pathname + reqUrl.search,
    headers: {
      'User-Agent': 'Mozilla/5.0 (Node.js)',
      'Accept': 'text/html',
    },
    port: 443,
    method: 'GET',
    rejectUnauthorized: false, // ⚠️ IGNORA VALIDAÇÃO SSL
    
  };

  const req = https.request(options, (res) => {
    // Define o encoding UTF-8 para garantir que caracteres especiais sejam lidos corretamente
    res.setEncoding('utf8');
    let html = '';
    res.on('data', chunk => html += chunk);
    res.on('end', () => callback(null, html));
  });

  req.on('error', (err) => {
    callback(err, null);
  });

  req.end();
}

function substringFrom(start, end, text) {
  const startIndex = text.indexOf(start);
  const endIndex = text.indexOf(end, startIndex + start.length);
  if (startIndex === -1 || endIndex === -1) {
    return null; // não encontrado
  }
  return text.substring(startIndex + start.length, endIndex).trim();
}

function extractNationality(text) {
  const imgEnd = text.indexOf('</img>') + 6; // Pega o fim da tag de imagem
  const textStart = text.indexOf('>', imgEnd) + 1; // Pega o texto após a tag
  const textEnd = text.indexOf('</', textStart);
  return textStart !== -1 && textEnd !== -1 ? text.slice(textStart, textEnd).trim() : null;
}

// Função para processar uma única página e extrair dados dos atletas (Lógica anterior colocada em uma função)
function processPage(html, pageNumber, athletesArr, gender) {
  console.log(`\n=== Processando página ${pageNumber} ===`);
  // mesma lógica de antes
  const content0 = substringFrom('<div class="table-wrapper">', '<!-- Search modal this one-->', html);
  const content1 = substringFrom('<tbody>', '</tbody>', content0);
  
  // Se não encontrar tbody, significa que não há mais dados
  if (!content1) {
    console.log(`Página ${pageNumber}: Nenhum dado encontrado (fim das páginas)`);
    return false; //  não há mais páginas
  }
  
  const rows = content1.split('</tr>');
  let athletesProcessed = 0;

  rows.forEach(element => {
    // Extrai os dados brutos do HTML
    const athleteID = substringFrom('data-id="', '"', element);
    const athletePosition = substringFrom ('<td data-th="Rank">', '</td>', element);
    const athleteName = substringFrom('<td data-th="Competitor">', '</td>', element);
    const athleteDateOfBirth = substringFrom('<td data-th="DOB">', '</td>', element);
    const athleteCountry = substringFrom(' data-athlete-url="/athletes/', '/', element);
    const athleteScore = substringFrom('<td data-th="score">', '</td>', element);
    const athleteEvent = substringFrom('<td data-th="EventList">', '</td>', element)
    const athleteUrl = substringFrom('data-athlete-url="', '"', element);

    // Só processa se encontrou dados válidos 
    if (athleteID) {
      // Aplica decodificação de entidades HTML nos nomes dos atletas
      const decodedName = decodeHtmlEntities(athleteName);

      console.log('\n\n-----')
      console.log('ID: ', athleteID);
      console.log('Posição:', athletePosition);
      console.log('Nome: ', decodedName); 
      console.log('Data de Nascimento: ', athleteDateOfBirth); 
      console.log('País:', athleteCountry); 
      console.log('Score: ', athleteScore);
      console.log('Evento:', athleteEvent); 
      console.log('URL perfil do atleta: ', athleteUrl);
      
      athletesArr.push({
        athlete_id: athleteID,
        gender: gender,
        athlete_position: athletePosition,
        athlete_name: decodedName,
        birth_date: athleteDateOfBirth,
        country: athleteCountry,
        score: athleteScore,
        event: athleteEvent,
        athlete_url: athleteUrl
      });
      athletesProcessed++; // conta quantos atletas foram processados
    }
  });

  console.log(`\nPágina ${pageNumber}: ${athletesProcessed} atletas processados`);
  return true; // Indica que a página foi processada com sucesso
}

// Função principal da paginação - agora retorna array de atletas
async function scrapeWorldAthletics(gender = 'men') {
  // Valida o parâmetro de gênero
  if (gender !== 'men' && gender !== 'women') throw new Error('Gênero inválido. Use "men" ou "women".');

  // Constrói a URL base específica para o gênero
  const baseUrl = `${baseUrlTemplate}/${gender}`;

  let currentPage = 1; //inicia na página 1
  let hasMorePages = true; //assume que há mais páginas
  const athletes = []; // Array para armazenar todos os atletas
  

  console.log(`Iniciando scraping do ranking ${gender === 'men' ? 'MASCULINO' : 'FEMININO'}...\n`);
  console.log(`URL base: ${baseUrl}\n`);

  while (hasMorePages) { //enquanto houver mais páginas 
    console.log(`Processando página ${currentPage}...`);
    
    // Constrói a URL da página atual
    const pageUrl = `${baseUrl}?page=${currentPage}`;
    console.log(`URL: ${pageUrl}`);

    // Faz a requisição HTTP
    const html = await new Promise((resolve, reject) => {
      fetchHTML(pageUrl, (err, html) => {
        if (err) reject(err);
        else resolve(html);
      });
    });

    console.log(`HTML recebido: ${html.length} caracteres`);

    // Processa a página atual e extrai os dados dos atletas
    const hasData = processPage(html, currentPage, athletes, gender);
  

    // Se não há mais dados, para o loop
    if (!hasData) {
      hasMorePages = false;
      console.log('Não há mais páginas com dados.');
    }

    // Se ainda há mais páginas, aguarda 2 segundos antes da próxima
    if (hasMorePages) {
      console.log(`\nAguardando 2 segundos antes da próxima página...\n`);
      await delay(2000);
    }

    currentPage++; // aumenta o número da página a cada volta no loop
  }
  
  console.log(` Scraping do ranking ${gender === 'men' ? 'MASCULINO' : 'FEMININO'} concluído! Total de páginas processadas: ${currentPage - 1}`);
  return athletes; // Retorna o array de atletas
}



// Função para processar ambos os gêneros sequencialmente
async function scrapeAllGenders() {
  console.log('Iniciando scraping completo - MASCULINO e FEMININO\n');
  console.log('=' .repeat(60));

  try {
    // Processa ranking masculino primeiro
    await scrapeWorldAthletics('men');

    console.log('\n' + '=' .repeat(60));
    console.log('Aguardando 5 segundos antes de processar o ranking feminino...\n');
    await delay(5000); // Delay maior entre gêneros para ser mais respeitoso com o servidor

    // Processa ranking feminino
    await scrapeWorldAthletics('women');

    console.log('\n' + '=' .repeat(60));
    console.log('SCRAPING FINALIZADO! Ambos os rankings foram processados com sucesso.');

  } catch (error) {
    console.error('Erro durante o scraping completo:', error.message);
  }
}

module.exports = { scrapeWorldAthletics };

// Chama a função para processar ambos os gêneros
// scrapeAllGenders(); // This line is now redundant as scrapeWorldAthletics handles the flow