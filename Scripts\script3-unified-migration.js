

const db = require('../models');

function calculateAge(birthDate) { // Função para calcular idade a partir da data de nascimento
  if (!birthDate) return null;

  try {
    const birth = new Date(birthDate);
    const today = new Date();

    // Verifica se a data é válida
    if (isNaN(birth.getTime())) return null;

    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    // Ajusta se ainda não fez aniversário este ano
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }

    return age > 0 && age < 150 ? age.toString() : null; // Validação básica
  } catch (error) {
    return null;
  }
}

// Remove valores nulos/indefinidos e espaços extras dos dados
function cleanData(value) {
  if (!value || value === 'null' || value === 'undefined') return null;
  return String(value).trim();
}

function isTimedEvent(record) { // Verifica se o evento é cronometrado (tem tempo registrado)
  return record.resultado_centesimos_seg !== null && record.resultado_centesimos_seg !== undefined;
}

// Mapeia evento da unified_athletes para evento olímpico específico com gênero
function getOlympicEventName(unifiedEvent, gender) {
  const eventMap = {
    '100 Metres': gender === 'women' ? '100m Feminino' : '100m Masculino',
    '200 Metres': gender === 'women' ? '200m Feminino' : '200m Masculino',
    '400 Metres': gender === 'women' ? '400m Feminino' : '400m Masculino',
    '800 Metres': gender === 'women' ? '800m Feminino' : '800m Masculino',
    '1500 Metres': gender === 'women' ? '1500m Feminino' : '1500m Masculino',
    '5000 Metres': gender === 'women' ? '5000m Feminino' : '5000m Masculino',
    '10,000 Metres': gender === 'women' ? '10.000m Feminino' : '10.000m Masculino',
    '10000 Metres': gender === 'women' ? '10.000m Feminino' : '10.000m Masculino',
    'Marathon': gender === 'women' ? 'Maratona Feminina' : 'Maratona Masculina',
    '100 Metres Hurdles': '100m com Barreira Feminino', // Apenas feminino
    '110 Metres Hurdles': '110m com Barreira Masculino', // Apenas masculino
    '400 Metres Hurdles': gender === 'women' ? '400m com Barreira Feminino' : '400m com Barreira Masculino',
    '3000 Metres Steeplechase': gender === 'women' ? '3000m com Obstáculos Feminino' : '3000m com Obstáculos Masculino'
  };

  return eventMap[unifiedEvent] || null;
}
//Função que calcula o score(cálculo 1 e 2)
async function calculateAthleteScore(record) {
  // Verifica se é uma prova cronometrada (tem tempo registrado)
  if (!isTimedEvent(record)) {
    return null; // Não processa provas sem tempo (saltos, arremessos, etc.)
  }
//cálculo 1 - se é medalhista olímpico
  if (record.has_olympic_medal === true || record.has_olympic_medal === 1) {
    // Determinar evento olímpico específico baseado no gênero
    const specificOlympicEvent = getOlympicEventName(record.event, record.gender);

    if (!specificOlympicEvent) {
      console.log(`    Evento não mapeado para medalhista: ${record.event} (${record.gender})`);
      return null;
    }

    let olympicResult = await db.OlympicsIndividual.findOne({ // busca medalhista olímpico
      where: { // filtra por nome e evento
        athlete_name: record.athlete_name, // nome do atleta
        event: specificOlympicEvent, // evento específico com gênero
        position: {
          [db.Sequelize.Op.between]: [1, 3] // Apenas medalhas (1º, 2º, 3º)
        }
      }
    });

    // Busca em equipe se não encontrou individual
    if (!olympicResult) {
      olympicResult = await db.OlympicsEquipe.findOne({
        where: {
          equipe: {
            [db.Sequelize.Op.like]: `%${record.athlete_name}%`
          },
          event: specificOlympicEvent, // evento específico com gênero
          position: {
            [db.Sequelize.Op.between]: [1, 3] // Apenas medalhas (1º, 2º, 3º)
          }
        }
      });
    }

    // Score = 101 - ((Posição - 1) × 0.5)

    if (olympicResult) {
      const position = parseInt(olympicResult.position);
      const score = 101 - ((position - 1) * 0.5);
      return score.toFixed(1); // Retorna com 1 casa decimal
    }
    return null; // Não encontrou posição olímpica

  } else { // Se não é medalhista olímpico, aplica o cálculo 2
    // ===== CÁLCULO 2: NÃO-MEDALHISTAS =====
   
    // Determinar o evento olímpico específico baseado no gênero do atleta
    const athleteGender = record.gender; // 'men' ou 'women'

    if (!athleteGender) {
      console.log(`    Atleta sem gênero definido: ${record.athlete_name}`);
      return null;
    }

    // Mapear evento da unified_athletes para evento olímpico específico
    const specificOlympicEvent = getOlympicEventName(record.event, athleteGender);

    if (!specificOlympicEvent) {
      console.log(`    Evento não mapeado: ${record.event} (${athleteGender})`);
      return null;
    }

    console.log(`    Atleta ${athleteGender === 'women' ? 'feminina' : 'masculino'}: ${specificOlympicEvent}`);


    
    // ===== ETAPA 1: BUSCAR TEMPO MAIS RÁPIDO (FA) =====
    // Replicando a mesma lógica do mais lento para garantir consistência
    const allAthletes = await db.UnifiedAthletes.findAll({
      where: {
        event: record.event,              // '100 Metres'
        gender: record.gender,            // 'men' ou 'women'
        resultado_centesimos_seg: {
          [db.Sequelize.Op.not]: null,
          [db.Sequelize.Op.gt]: 0
        }
      },
      attributes: ['athlete_name', 'resultado_centesimos_seg'],
      raw: true
    });

    if (allAthletes.length === 0) {
      console.log(`    Nenhum atleta encontrado para ${record.event} (${record.gender})`);
      return null;
    }

    // Encontra o atleta com o menor tempo (mais rápido) em centésimos
    const fastestTime = Math.min(...allAthletes.map(a => parseInt(a.resultado_centesimos_seg)));
    const fastestAthlete = allAthletes.find(a => parseInt(a.resultado_centesimos_seg) === fastestTime);

    console.log(`    MAIS RÁPIDO (FA): ${fastestAthlete.athlete_name} - ${fastestTime} centésimos (${(fastestTime/100).toFixed(2)}s)`);

    
    // ===== ETAPA 2: BUSCAR TEMPO MAIS LENTO (LA) =====
    // Usa a mesma lista de atletas já carregada

    // Encontra o atleta com o maior tempo (mais lento) em centésimos (usando a mesma lista)
    const slowestTime = Math.max(...allAthletes.map(a => parseInt(a.resultado_centesimos_seg)));
    const slowestAthlete = allAthletes.find(a => parseInt(a.resultado_centesimos_seg) === slowestTime);

    // Tempo do atleta atual que está sendo avaliado
    const athleteTime = parseInt(record.resultado_centesimos_seg);

    console.log(`    MAIS LENTO (LA): ${slowestAthlete.athlete_name} - ${slowestTime} centésimos (${(slowestTime/100).toFixed(2)}s)`);

    console.log(`    ATLETA ATUAL (TEM): ${record.athlete_name} - ${athleteTime} centésimos (${(athleteTime/100).toFixed(2)}s)`);
    console.log(`    RANGE: ${slowestTime - fastestTime} centésimos`);

   
    // Fórmula: Score = ((LA - TEM) / (LA - FA)) × 100

    if (slowestTime !== fastestTime) { // Evita divisão por zero
      const numerador = slowestTime - athleteTime;   // Diferença entre mais lento e atleta atual
      const denominador = slowestTime - fastestTime; // Range total de tempos
      const score = (numerador / denominador) * 100; // Percentual dentro do range

      // Limita score máximo a 100 apenas se for maior que 100
      // Scores normais (menores que 100) devem ser mantidos
      if (score > 100) {
        return "100.0";  // Atletas mais rápidos que ouro = score 100
      } else {
        return Math.max(0, score).toFixed(1);  // Score normal, apenas evita negativos
      }
    }

    return null; // Não foi possível calcular (tempos iguais)
  }
}
// Mapeia eventos da unified_athletes para eventos olímpicos específicos
function normalizeEventName(eventName) {
  if (!eventName) return null;

  const eventMap = {
    // Corridas de velocidade
    '100 Metres': ['100m Masculino', '100m Feminino'],
    '200 Metres': ['200m Masculino', '200m Feminino'],
    '400 Metres': ['400m Masculino', '400m Feminino'],

    // Meio-fundo
    '800 Metres': ['800m Masculino', '800m Feminino'],
    '1500 Metres': ['1500m Masculino', '1500m Feminino'],

    // Fundo
    '5000 Metres': ['5000m Masculino', '5000m Feminino'],
    '10,000 Metres': ['10.000m Masculino', '10.000m Feminino'],
    '10000 Metres': ['10.000m Masculino', '10.000m Feminino'],

    // Barreiras
    '100 Metres Hurdles': ['100m com Barreira Feminino'],
    '110 Metres Hurdles': ['110m com Barreira Masculino'],
    '400 Metres Hurdles': ['400m com Barreira Masculino', '400m com Barreira Feminino'],

    // Obstáculos
    '3000 Metres Steeplechase': ['3000m com Obstáculos Masculino', '3000m com Obstáculos Feminino'],

    // Marcha
    '20 Kilometres Race Walk': ['20km Marcha Atlética Masculina', '20km Marcha Atlética Feminina'],

    // Saltos
    'Long Jump': ['Salto em Distância Masculino', 'Salto em Distância Feminino'],
    'High Jump': ['Salto em Altura Masculino', 'Salto em Altura Feminino'],
    'Pole Vault': ['Salto com Vara Masculino', 'Salto com Vara Feminino'],
    'Triple Jump': ['Salto Triplo Masculino', 'Salto Triplo Feminino'],

    // Arremessos
    'Shot Put': ['Arremesso de Peso Masculino', 'Arremesso de Peso Feminino'],
    'Discus Throw': ['Lançamento de Disco Masculino', 'Lançamento de Disco Feminino'],
    'Hammer Throw': ['Lançamento de Martelo Masculino', 'Lançamento de Martelo Feminino'],
    'Javelin Throw': ['Lançamento de Dardo Masculino', 'Lançamento de Dardo Feminino'],

    // Combinadas
    'Decathlon': ['Decatlo Masculino'],
    'Heptathlon': ['Heptatlo Feminino'],

    // Maratona
    'Marathon': ['Maratona Masculina', 'Maratona Feminina'],

    // Revezamentos
    '4x100 Metres Relay': ['Revezamento 4x100m Masculino', 'Revezamento 4x100m Feminino'],
    '4x400 Metres Relay': ['Revezamento 4x400m Masculino', 'Revezamento 4x400m Feminino'],
    'Mixed 4x400 Metres Relay': ['Revezamento 4x400m Misto'],
    'Marathon Race Walk Relay Mixed': ['Maratona de Marcha Atlética Revezamento Misto']
  };

  return eventMap[eventName] || [];
}

function convertTimeToHundredths(resultMark, eventName) { // Converte tempos de diferentes formatos para centésimos de segundo
  if (!resultMark || !eventName) return null;

  // Lista de eventos cronometrados (apenas provas de tempo)
  const timeEvents = [
    // Provas de velocidade
    '60 Metres', '100 Metres', '200 Metres', '400 Metres',
    // Provas de meio-fundo e fundo
    '800 Metres', '1500 Metres', 'Mile', '3000 Metres', '5000 Metres',
    '10,000 Metres', '10000 Metres', 'Marathon', 'Half Marathon',
    // Provas com barreiras
    '60 Metres Hurdles', '100 Metres Hurdles', '110 Metres Hurdles',
    '300 Metres Hurdles', '400 Metres Hurdles',
    // Provas com obstáculos
    '3000 Metres Steeplechase',
    // Marcha atlética
    '20 Kilometres Race Walk', '35 Kilometres Race Walk',
    // Revezamentos
    '4x100 Metres Relay', '4x400 Metres Relay', 'Mixed 4x400 Metres Relay',
    // Provas indoor/especiais
    'Mile Short Track', '3000 Metres Short Track'
  ];

  // Verifica se é um evento cronometrado
  const isTimeEvent = timeEvents.some(event =>
    eventName.toLowerCase().includes(event.toLowerCase()) ||
    event.toLowerCase().includes(eventName.toLowerCase())
  );

  if (!isTimeEvent) {
    return null; // Não é prova cronometrada (saltos, arremessos, etc.)
  }

  try {
    // Remove espaços e caracteres especiais
    const cleanTime = resultMark.trim();

    // Formato com dois pontos: pode ser MM:SS.CC ou H:MM:SS
    if (cleanTime.includes(':')) {
      const parts = cleanTime.split(':');

      // Formato H:MM:SS (APENAS para Marathon - 3 partes)
      if (parts.length === 3 && eventName.toLowerCase().includes('marathon')) {
        const hours = parseInt(parts[0]);
        const minutes = parseInt(parts[1]);
        const seconds = parseInt(parts[2]);

        return (hours * 60 * 60 * 100) + (minutes * 60 * 100) + (seconds * 100);
      }

      // Formato MM:SS.CC (Provas curtas - 2 partes)
      if (parts.length === 2) {
        const minutes = parseInt(parts[0]);
        const secondsParts = parts[1].split('.');
        const seconds = parseInt(secondsParts[0]);
        const hundredths = secondsParts[1] ? parseInt(secondsParts[1].padEnd(2, '0').substring(0, 2)) : 0;

        return (minutes * 60 * 100) + (seconds * 100) + hundredths;
      }
    }

    // Formato: SS.CC (ex: 10.23)
    if (cleanTime.includes('.')) {
      const parts = cleanTime.split('.');
      const seconds = parseInt(parts[0]);
      const hundredths = parts[1] ? parseInt(parts[1].padEnd(2, '0').substring(0, 2)) : 0;

      return (seconds * 100) + hundredths;
    }

    // Formato: apenas segundos (ex: 45)
    const seconds = parseInt(cleanTime);
    if (!isNaN(seconds)) {
      return seconds * 100;
    }

    return null; // Não conseguiu converter

  } catch (error) {
    console.log(`Erro ao converter tempo "${resultMark}" do evento "${eventName}": ${error.message}`);
    return null;
  }
}

// Etapa 1 que migra dados de world_athletics + test_results para unified_athletes

async function migrateCompleteData() { // Função principal da Etapa 1
  console.log('=== ETAPA 1: MIGRAÇÃO COMPLETA (WORLD_ATHLETICS + TEST_RESULTS) ===\n');

  try {
    // Busca TODOS os atletas da world_athletics (sem filtros de ID)
    console.log('Buscando TODOS os atletas da tabela world_athletics...');
    const worldAthletes = await db.WorldAthletics.findAll({
      attributes: ['athlete_id', 'athlete_name', 'athlete_position'],
      order: [['id', 'ASC']] // Ordem de inserção (= ordem do site)
    });
    console.log(`Encontrados ${worldAthletes.length} atletas na world_athletics`);

    if (worldAthletes.length === 0) {
      console.log('Nenhum atleta encontrado na tabela world_athletics');
      return { processed: 0, updated: 0, noResults: 0, errors: 0 };
    }

    let processed = 0;
    let inserted = 0;
    let updated = 0;
    let noResults = 0;
    let errors = 0;

    console.log('\nIniciando migração dos resultados (ordem do ranking)...\n');

    for (const athlete of worldAthletes) {
      try {
        processed++;

        // Busca todos os resultados deste atleta na test_results
        const testResults = await db.TestResults.findAll({
          where: { athlete_id: athlete.athlete_id },
          order: [['competition_date', 'DESC']] // Mais recentes primeiro
        });

        if (testResults.length === 0) {
          noResults++;
          continue;
        }

        // Agrupa resultados por disciplina e pega o mais recente de cada uma
        const latestResultsByEvent = {};

        for (const result of testResults) {
          const discipline = cleanData(result.discipline);
          const resultMark = cleanData(result.result_mark);
          const competitionDate = result.competition_date;

          if (!discipline || !resultMark) continue;

          // Se ainda não temos resultado para esta disciplina, ou se este é mais recente
          if (!latestResultsByEvent[discipline] ||
              new Date(competitionDate) > new Date(latestResultsByEvent[discipline].date)) {
            latestResultsByEvent[discipline] = {
              mark: resultMark,
              date: competitionDate
            };
          }
        }

        // Se não conseguiu processar nenhum resultado válido
        if (Object.keys(latestResultsByEvent).length === 0) {
          noResults++;
          continue;
        }

        // Busca dados completos do atleta na world_athletics
        const athleteData = await db.WorldAthletics.findOne({
          where: { athlete_id: athlete.athlete_id },
          attributes: ['athlete_name', 'birth_date', 'country', 'score', 'gender']
        });

        if (!athleteData) {
          console.log(`Atleta ${athlete.athlete_name} não encontrado na world_athletics`);
          noResults++;
          continue;
        }

        // Calcula idade
        const age = calculateAge(athleteData.birth_date);

        // Remove registro básico sem eventos para manter organização
        await db.UnifiedAthletes.destroy({
          where: {
            athlete_id: athlete.athlete_id,
            event: null // Remove apenas registros sem evento específico
          }
        });

        // Insere uma linha para cada evento (em ordem)
        for (const [event, eventData] of Object.entries(latestResultsByEvent)) {
          // Converte tempo para centésimos se for prova de tempo
          const centesimos = convertTimeToHundredths(eventData.mark, event);

          const [, created] = await db.UnifiedAthletes.upsert({
            athlete_id: athlete.athlete_id,
            athlete_name: cleanData(athleteData.athlete_name),
            age: age,
            country: cleanData(athleteData.country),
            gender: cleanData(athleteData.gender), // Campo gender adicionado
            world_score: cleanData(athleteData.score),
            event: event,
            result_mark: eventData.mark,
            competition_date: eventData.date,
            resultado_centesimos_seg: centesimos, // Convertido automaticamente
            calculation_score: null, // Preenchido na Etapa 3
            instagram_followers: null, // Funcionalidade futura
            is_olympic_athlete: false, // Preenchido na Etapa 2
            has_olympic_medal: false // Preenchido na Etapa 2
          });

          if (created) {
            inserted++;
          } else {
            updated++;
          }

          // Log da conversão para debug
          if (centesimos !== null) {
            console.log(`  - ${event}: ${eventData.mark} → ${centesimos} centésimos`);
          }
        }

        updated++;
        console.log(`Atualizado: ${athlete.athlete_name} - ${Object.keys(latestResultsByEvent).length} eventos`);

        // Log de progresso a cada 1000 atletas
        if (processed % 1000 === 0) {
          console.log(`Progresso: ${processed}/${worldAthletes.length} atletas processados (${updated} atualizados, ${noResults} sem resultados)`);
        }

      } catch (error) {
        errors++;
        console.error(`Erro ao processar resultados do atleta ${athlete.athlete_name} (ID: ${athlete.athlete_id}):`, error.message);
      }
    }

    return { processed, inserted, updated, noResults, errors };

  } catch (error) {
    console.error('Erro durante a migração da test_results:', error.message);
    throw error;
  }
}

// etapa 2 que verifica se é atleta olímpico e medalhista
async function migrateOlympicData() {
  console.log('=== ETAPA 2: VERIFICANDO DADOS OLÍMPICOS ===\n');

  try {
    // Busca todos os registros da UnifiedAthletes (linha por linha, evento por evento)
    console.log('Buscando registros da tabela unified_athletes...');
    const unifiedRecords = await db.UnifiedAthletes.findAll({
      attributes: ['id', 'athlete_id', 'athlete_name', 'event'],
      where: {
        event: { [db.Sequelize.Op.not]: null } // Apenas registros com eventos específicos
      }
    });
    console.log(`Encontrados ${unifiedRecords.length} registros na unified_athletes`);

    if (unifiedRecords.length === 0) { // Verifica se encontrou registros
      console.log('Nenhum registro encontrado na tabela unified_athletes');
      return { processed: 0, olympicAthletes: 0, medalists: 0, errors: 0 };
    }

    let processed = 0;
    let olympicAthletes = 0;
    let medalists = 0;
    let errors = 0;

    console.log('\nIniciando verificação olímpica por evento...\n');

    for (const record of unifiedRecords) { // Para cada registro (atleta + evento)
      try {
        processed++; // Incrementa registro processado

        let isOlympic = false;
        let hasMedal = false;

        // Normaliza o nome do evento para busca olímpica
        const olympicEventNames = normalizeEventName(record.event);

        if (olympicEventNames.length === 0) {
          // Evento não mapeado, pula
          continue;
        }

        // Verifica se aparece na olympics_individual NESTE EVENTO ESPECÍFICO
        const individualResults = await db.OlympicsIndividual.findAll({
          where: {
            athlete_name: record.athlete_name,
            event: {
              [db.Sequelize.Op.in]: olympicEventNames  // BUSCA POR EVENTOS NORMALIZADOS
            }
          }
        });

        // Verifica se aparece na olympics_equipe NESTE EVENTO ESPECÍFICO
        const teamResults = await db.OlympicsEquipe.findAll({
          where: {
            equipe: {
              [db.Sequelize.Op.like]: `%${record.athlete_name}%`
            },
            event: {
              [db.Sequelize.Op.in]: olympicEventNames  // ← BUSCA POR EVENTOS NORMALIZADOS
            }
          }
        });

        // Se aparece em qualquer uma das tabelas, é olímpico
        if (individualResults.length > 0 || teamResults.length > 0) {
          isOlympic = true;
          olympicAthletes++;

          // Verifica se tem medalha (posição 1, 2 ou 3)
          const allResults = [...individualResults, ...teamResults];

          for (const result of allResults) { // Para cada resultado
            const position = parseInt(result.position); // Posição do atleta
            if (position >= 1 && position <= 3) { // Se está entre 1 e 3
              hasMedal = true; // Marca como medalhista olímpico
              break;
            }
          }

          if (hasMedal) { // Se é medalhista
            medalists++; 
            console.log(`Medalhista olímpico: ${record.athlete_name} - ${record.event}`);
          } else {
            console.log(`Atleta olímpico: ${record.athlete_name} - ${record.event}`);
          }
        }

        // atualiza na tabela unified_athletes se é atleta olímpico e medalhista
        await db.UnifiedAthletes.update({
          is_olympic_athlete: isOlympic,
          has_olympic_medal: hasMedal
        }, {
          where: {
            athlete_id: record.athlete_id,
            event: record.event  // ← ATUALIZA APENAS ESTA LINHA ESPECÍFICA
          }
        });

        // Log de progresso a cada 50 registros
        if (processed % 50 === 0) {
          console.log(`Progresso: ${processed}/${unifiedRecords.length} registros processados (${olympicAthletes} olímpicos, ${medalists} medalhistas)`);
        }

      } catch (error) {
        errors++;
        console.error(`Erro ao verificar registro ${record.athlete_name} - ${record.event} (ID: ${record.athlete_id}):`, error.message);
      }
    }

    return { processed, olympicAthletes, medalists, errors };

  } catch (error) {
    console.error('Erro durante a verificação olímpica:', error.message);
    throw error;
  }
}

// Etapa 3 que calcula o score
async function calculateScores() { 
  console.log('=== ETAPA 3: CALCULANDO SCORES DOS ATLETAS ===\n');

  try {
    // Buscar TODOS os atletas de eventos cronometrados
    console.log('Buscando atletas de eventos cronometrados...');
    const records = await db.UnifiedAthletes.findAll({
      where: {
        resultado_centesimos_seg: {
          [db.Sequelize.Op.not]: null
        },
        gender: {
          [db.Sequelize.Op.in]: ['men', 'women'] // Apenas atletas com gênero definido
        }
      },
      attributes: ['id', 'athlete_id', 'athlete_name', 'event', 'gender', 'resultado_centesimos_seg', 'has_olympic_medal']
    });

    console.log(`Encontrados ${records.length} atletas de eventos cronometrados`);

    if (records.length === 0) { //
      console.log('Nenhum registro encontrado para calcular scores');
      return { processed: 0, calculated: 0, skipped: 0, errors: 0 };
    }

    let processed = 0;
    let calculated = 0;
    let skipped = 0;
    let errors = 0;

    console.log('\nIniciando cálculo de scores...\n');

    for (const record of records) {
      try {
        processed++;

        const score = await calculateAthleteScore(record);

        if (score !== null) {
          await db.UnifiedAthletes.update({
            calculation_score: score
          }, {
            where: { id: record.id }
          });
          calculated++;
        } else {
          skipped++;
        }

        // Log de progresso a cada 100 registros
        if (processed % 100 === 0) {
          console.log(`Progresso: ${processed}/${records.length} registros processados (${calculated} calculados, ${skipped} pulados)`);
        }

      } catch (error) {
        errors++;
        console.error(`Erro ao calcular score para ${record.athlete_name} - ${record.event}:`, error.message);
      }
    }



    return { processed, calculated, skipped, errors };

  } catch (error) {
    console.error('Erro durante o cálculo de scores:', error.message);
    throw error;
  }
}


// função principal da etapa 3	
// Executa o cálculo de scores para atletas olímpicos do 100m Masculino
async function runEtapa3() {
  console.log('UNIFIED MIGRATION');
  console.log('Etapa 3: Cálculo de scores\n');

  try {
    // Conecta ao banco
    console.log('Conectando ao banco...');
    await db.sequelize.authenticate();
    console.log('Conexão estabelecida com sucesso!\n');

    // Executa o cálculo de scores
    const stats = await calculateScores();

    // Mostra estatísticas finais
    console.log('\n=== ESTATÍSTICAS FINAIS ===');
    console.log(`Registros processados: ${stats.processed}`);
    console.log(`Scores calculados: ${stats.calculated}`);
    console.log(`Registros pulados: ${stats.skipped}`);
    console.log(`Erros: ${stats.errors}`);
    console.log('\nEtapa 3 concluída com sucesso!');

    if (stats.errors > 0) {
      console.log(`\nAtenção: ${stats.errors} registros tiveram problemas durante o cálculo.`);
    }

  } catch (error) {
    console.error('Erro na Etapa 3:', error.message);
    process.exit(1);
  } finally {
    await db.sequelize.close();
    console.log('\nConexão fechada.');
  }
}

// função principal da etapa 2, que verifica atletas olímpicos e medalhistas
async function runEtapa2() {
  console.log('UNIFIED MIGRATION');
  console.log('Etapa 2: Verificação olímpica\n');

  try {
    // Conecta ao banco
    console.log('Conectando ao banco...');
    await db.sequelize.authenticate();
    console.log('Conexão estabelecida com sucesso!\n');

    // Debug: mostra quais models estão disponíveis
    console.log('Models disponíveis:', Object.keys(db).filter(key => key !== 'sequelize' && key !== 'Sequelize'));
    console.log('');

    // Executa a migração
    const stats = await migrateOlympicData();

    // Mostra estatísticas finais
    console.log('\n=== ESTATÍSTICAS FINAIS ===');
    console.log(`Atletas processados: ${stats.processed}`);
    console.log(`Atletas olímpicos encontrados: ${stats.olympicAthletes}`);
    console.log(`Medalhistas olímpicos: ${stats.medalists}`);
    console.log(`Erros: ${stats.errors}`);
    console.log('\nEtapa 2 concluída com sucesso!');

    if (stats.olympicAthletes > 0) {
      const percentage = ((stats.olympicAthletes / stats.processed) * 100).toFixed(2);
      console.log(`\nInfo: ${percentage}% dos atletas são olímpicos.`);
    }

    if (stats.medalists > 0) {
      const medalPercentage = ((stats.medalists / stats.olympicAthletes) * 100).toFixed(2);
      console.log(`Info: ${medalPercentage}% dos atletas olímpicos são medalhistas.`);
    }

    if (stats.errors > 0) {
      console.log(`\nAtenção: ${stats.errors} atletas tiveram problemas durante o processamento.`);
    }

  } catch (error) {
    console.error('\nErro durante a execução da Etapa 3:', error.message);
    process.exit(1);
  } finally {
    await db.sequelize.close();
    console.log('\nConexão fechada.');
  }
}

// função principal da etapa 1, que executa a migração completa
async function runEtapa1() {
  console.log('UNIFIED MIGRATION');
  console.log('Etapa 1: Migração completa (world_athletics + test_results)\n');

  try {
    // Conecta ao banco
    console.log('Conectando ao banco...');
    await db.sequelize.authenticate();
    console.log('Conexão estabelecida com sucesso!\n');

    // Executa a migração completa
    const stats = await migrateCompleteData();

    // Mostra estatísticas finais
    console.log('\n=== ESTATÍSTICAS FINAIS ===');
    console.log(`Atletas processados: ${stats.processed}`);
    console.log(`Atletas sem resultados: ${stats.noResults}`);
    console.log(`Erros: ${stats.errors}`);
    console.log('\nEtapa 2 concluída com sucesso!');

    if (stats.noResults > 0) {
      console.log(`\nInfo: ${stats.noResults} atletas não possuem resultados na test_results.`);
    }

    if (stats.errors > 0) {
      console.log(`\nAtenção: ${stats.errors} atletas tiveram problemas durante o processamento.`);
    }

  } catch (error) {
    console.error('\nErro durante a execução da Etapa 2:', error.message);
    process.exit(1);
  } finally {
    await db.sequelize.close();
    console.log('\nConexão fechada.');
  }
}



// Executa se chamado diretamente via linha de comando
if (require.main === module) {
  // Verifica qual etapa executar baseado no argumento
  const etapa = process.argv[2];

  if (etapa === 'etapa1') {
    runEtapa1();
  } else if (etapa === 'etapa2') {
    runEtapa2();
  } else if (etapa === 'etapa3') {
    runEtapa3();
  } else {
    console.log('Uso: node script3-unified-migration.js [etapa1|etapa2|etapa3]');
    console.log('');
    console.log('Etapas disponíveis:');
    console.log('  etapa1 - Migração completa (world_athletics + test_results)');
    console.log('  etapa2 - Verifica dados olímpicos');
    console.log('  etapa3 - Calcula scores dos atletas');
    console.log('');
    console.log('Exemplos:');
    console.log('  node Scripts/script3-unified-migration.js etapa1');
    console.log('  node Scripts/script3-unified-migration.js etapa2');
    console.log('  node Scripts/script3-unified-migration.js etapa3');
  }
}

// Exporta todas as funções para uso em outros módulos
module.exports = {
  // Funções principais das etapas
  migrateCompleteData,    // Etapa 1: Migração dos dados básicos
  migrateOlympicData,     // Etapa 2: Identificação de atletas olímpicos
  calculateScores,        // Etapa 3: Cálculo de scores

  // Funções auxiliares
  calculateAge,           // Calcula idade do atleta
  cleanData,              // Limpa e valida dados
  normalizeEventName,     // Mapeia eventos para nomes olímpicos
  convertTimeToHundredths,// Converte tempos para centésimos
  isTimedEvent,           // Verifica se é prova cronometrada
  calculateAthleteScore,  // Calcula score individual do atleta

  // Funções de execução
  runEtapa1,              // Executa Etapa 1 completa
  runEtapa2,              // Executa Etapa 2 completa
  runEtapa3               // Executa Etapa 3 completa
};
